"""
证件照生成相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Optional
import logging
import base64
import io

from app.database import get_db
from app.models import User, UserAction
from app.auth import get_current_user
from app.schemas.idphoto import (
    IDPhotoRequest,
    IDPhotoResponse,
    IDPhotoSize,
    IDPhotoColor
)
from app.services.idphoto_service import idphoto_service
from app.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/idphoto",
    tags=["证件照"],
    responses={404: {"description": "未找到"}},
)

def record_user_action(db: Session, user: User, action_type: str, action_content: Optional[dict] = None):
    """记录用户行为"""
    try:
        user_action = UserAction(
            user_id=user.id,
            action_type=action_type,
            action_content=action_content
        )
        db.add(user_action)
        db.commit()
        logger.info(f"记录用户 {user.id} 行为: {action_type}")
    except Exception as e:
        logger.warning(f"记录用户行为失败: {e}")
        db.rollback()

@router.get("/health")
async def health_check():
    """
    健康检查接口
    """
    return {"status": "ok", "message": "证件照服务运行正常"}

@router.post("/generate", response_model=IDPhotoResponse)
async def generate_idphoto(
    image: UploadFile = File(..., description="上传的照片文件"),
    size: IDPhotoSize = Form(default=IDPhotoSize.ONE_INCH, description="证件照尺寸"),
    color: IDPhotoColor = Form(default=IDPhotoColor.WHITE, description="背景颜色"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    生成证件照

    支持的尺寸：
    - 一寸 (295x413)
    - 两寸 (413x579)
    - 大一寸 (390x567)
    - 小一寸 (260x378)
    - 大两寸 (413x626)
    - 小两寸 (413x531)

    支持的颜色：
    - 透明
    - 白色
    - 蓝色
    - 红色
    - 蓝色渐变
    - 红色渐变
    """
    try:
        logger.info(f"用户 {current_user.id} 请求生成证件照，尺寸: {size}, 颜色: {color}")

        # 验证文件类型
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请上传有效的图片文件"
            )

        # 验证文件大小 (限制为10MB)
        if image.size and image.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="图片文件大小不能超过10MB"
            )

        # 获取尺寸配置
        size_config = settings.IDPHOTO_SIZES.get(size.value)
        if not size_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的尺寸: {size.value}"
            )

        # 获取颜色配置
        color_config = settings.IDPHOTO_COLORS.get(color.value)
        if not color_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的颜色: {color.value}"
            )

        # 调用证件照服务生成证件照
        result = await idphoto_service.generate_idphoto(
            image.file,
            size_config,
            color_config
        )

        if not result.get("status"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"证件照生成失败: {result.get('error', '未知错误')}"
            )

        # 记录用户行为
        record_user_action(
            db=db,
            user=current_user,
            action_type="generate_idphoto",
            action_content={
                "size": size.value,
                "color": color.value,
                "filename": image.filename
            }
        )

        # 构建响应数据
        response_data = {
            "image_base64": result.get("image_base64"),
            "size": size.value,
            "color": color.value,
            "dimensions": {
                "width": size_config["width"],
                "height": size_config["height"]
            }
        }

        # 如果有高清版本，也包含进去
        if result.get("hd_base64"):
            response_data["hd_image_base64"] = result.get("hd_base64")

        # 如果有透明版本，也包含进去
        if result.get("transparent_base64"):
            response_data["transparent_base64"] = result.get("transparent_base64")

        logger.info(f"用户 {current_user.id} 证件照生成成功")

        return IDPhotoResponse(
            success=True,
            message="证件照生成成功",
            data=response_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"生成证件照时出错: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成证件照时出错: {str(e)}"
        )

@router.get("/sizes")
async def get_available_sizes():
    """
    获取支持的证件照尺寸列表
    """
    return {
        "success": True,
        "message": "获取尺寸列表成功",
        "data": {
            "sizes": [
                {
                    "name": "一寸",
                    "value": "一寸",
                    "width": 295,
                    "height": 413,
                    "description": "标准一寸证件照"
                },
                {
                    "name": "两寸",
                    "value": "两寸",
                    "width": 413,
                    "height": 579,
                    "description": "标准两寸证件照"
                },
                {
                    "name": "大一寸",
                    "value": "大一寸",
                    "width": 390,
                    "height": 567,
                    "description": "大一寸证件照"
                },
                {
                    "name": "小一寸",
                    "value": "小一寸",
                    "width": 260,
                    "height": 378,
                    "description": "小一寸证件照"
                },
                {
                    "name": "大两寸",
                    "value": "大两寸",
                    "width": 413,
                    "height": 626,
                    "description": "大两寸证件照"
                },
                {
                    "name": "小两寸",
                    "value": "小两寸",
                    "width": 413,
                    "height": 531,
                    "description": "小两寸证件照"
                }
            ]
        }
    }

@router.get("/colors")
async def get_available_colors():
    """
    获取支持的背景颜色列表
    """
    return {
        "success": True,
        "message": "获取颜色列表成功",
        "data": {
            "colors": [
                {
                    "name": "透明",
                    "value": "透明",
                    "hex": None,
                    "render": 0,
                    "description": "透明背景"
                },
                {
                    "name": "白色",
                    "value": "白色",
                    "hex": "FFFFFF",
                    "render": 0,
                    "description": "白色背景"
                },
                {
                    "name": "蓝色",
                    "value": "蓝色",
                    "hex": "438EDB",
                    "render": 0,
                    "description": "蓝色背景"
                },
                {
                    "name": "红色",
                    "value": "红色",
                    "hex": "FF0000",
                    "render": 0,
                    "description": "红色背景"
                },
                {
                    "name": "蓝色渐变",
                    "value": "蓝色渐变",
                    "hex": "438EDB",
                    "render": 1,
                    "description": "蓝色渐变背景"
                },
                {
                    "name": "红色渐变",
                    "value": "红色渐变",
                    "hex": "FF0000",
                    "render": 1,
                    "description": "红色渐变背景"
                }
            ]
        }
    }
