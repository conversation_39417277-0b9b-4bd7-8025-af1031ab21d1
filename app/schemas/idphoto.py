"""
证件照相关的数据模式
"""
from pydantic import BaseModel, Field
from typing import Optional, Union
from enum import Enum

class IDPhotoSize(str, Enum):
    """证件照尺寸枚举"""
    ONE_INCH = "一寸"
    TWO_INCH = "两寸"
    BIG_ONE_INCH = "大一寸"
    SMALL_ONE_INCH = "小一寸"
    BIG_TWO_INCH = "大两寸"
    SMALL_TWO_INCH = "小两寸"

class IDPhotoColor(str, Enum):
    """证件照颜色枚举"""
    TRANSPARENT = "透明"
    WHITE = "白色"
    BLUE = "蓝色"
    RED = "红色"
    BLUE_GRADIENT = "蓝色渐变"
    RED_GRADIENT = "红色渐变"

class IDPhotoRequest(BaseModel):
    """证件照生成请求"""
    size: IDPhotoSize = Field(default=IDPhotoSize.ONE_INCH, description="证件照尺寸")
    color: IDPhotoColor = Field(default=IDPhotoColor.WHITE, description="背景颜色")
    
    class Config:
        use_enum_values = True

class IDPhotoResponse(BaseModel):
    """证件照生成响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[dict] = Field(None, description="响应数据")
    
    class Config:
        json_encoders = {
            # 可以添加自定义编码器
        }

class IDPhotoProcessRequest(BaseModel):
    """证件照处理请求（内部使用）"""
    input_image_base64: str = Field(..., description="输入图片的base64编码")
    width: int = Field(..., description="目标宽度")
    height: int = Field(..., description="目标高度")
    color: Optional[str] = Field(None, description="背景颜色HEX值")
    render: int = Field(default=0, description="渲染模式：0-纯色，1-上下渐变，2-中心渐变")
    
class IDPhotoProcessResponse(BaseModel):
    """证件照处理响应（内部使用）"""
    status: bool = Field(..., description="处理状态")
    image_base64_standard: Optional[str] = Field(None, description="标准证件照base64")
    image_base64_hd: Optional[str] = Field(None, description="高清证件照base64")
    image_base64: Optional[str] = Field(None, description="最终图片base64")
    error: Optional[str] = Field(None, description="错误信息")
