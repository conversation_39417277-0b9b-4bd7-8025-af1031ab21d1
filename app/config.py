"""
应用配置文件 (已弃用)
请使用 config/fastapi_config.py 中的新配置
"""
import os
import warnings
from dotenv import load_dotenv

# 发出弃用警告
warnings.warn(
    "app/config.py 已弃用，请使用 config/fastapi_config.py 中的新配置",
    DeprecationWarning,
    stacklevel=2
)

# 为了向后兼容，导入新配置
try:
    from config.fastapi_config import settings as new_settings, validate_config as new_validate_config

    # 创建兼容性包装器
    class Settings:
        def __init__(self):
            # 将新配置的属性映射到旧的属性名
            self.APP_NAME = new_settings.APP_NAME
            self.VERSION = new_settings.VERSION
            self.DEBUG = new_settings.DEBUG
            self.DATABASE_URL = new_settings.DATABASE_URL
            self.SECRET_KEY = new_settings.SECRET_KEY
            self.ALGORITHM = new_settings.ALGORITHM
            self.ACCESS_TOKEN_EXPIRE_MINUTES = new_settings.ACCESS_TOKEN_EXPIRE_MINUTES
            self.WECHAT_APP_ID = new_settings.WECHAT_APP_ID
            self.WECHAT_APP_SECRET = new_settings.WECHAT_APP_SECRET
            self.WECHAT_API_URL = new_settings.WECHAT_API_URL
            self.HOST = new_settings.HOST
            self.PORT = new_settings.PORT
            self.PDF_SERVICE_URL = new_settings.PDF_SERVICE_URL
            self.IDPHOTO_SERVICE_URL = new_settings.IDPHOTO_SERVICE_URL
            self.IDPHOTO_SIZES = new_settings.IDPHOTO_SIZES
            self.IDPHOTO_COLORS = new_settings.IDPHOTO_COLORS

    settings = Settings()
    validate_config = new_validate_config

except ImportError:
    # 如果新配置不可用，回退到旧配置
    load_dotenv()

    class Settings:
        """应用设置"""

        # 应用基本配置
        APP_NAME: str = "Resume Service API"
        VERSION: str = "1.0.0"
        DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"

        # 数据库配置
        DATABASE_URL: str = os.getenv(
            "DATABASE_URL",
            "mysql+pymysql://resume_user:Resume123!@localhost/resume_service"
        )

        # JWT配置
        SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
        ALGORITHM: str = "HS256"
        ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

        # 微信小程序配置
        WECHAT_APP_ID: str = os.getenv("WECHAT_APP_ID", "")
        WECHAT_APP_SECRET: str = os.getenv("WECHAT_APP_SECRET", "")
        WECHAT_API_URL: str = "https://api.weixin.qq.com/sns/jscode2session"

        # 服务器配置
        HOST: str = os.getenv("FASTAPI_HOST", os.getenv("HOST", "0.0.0.0"))
        PORT: int = int(os.getenv("FASTAPI_PORT", os.getenv("PORT", "18080")))

        # PDF服务配置
        PDF_SERVICE_URL: str = os.getenv("PDF_SERVICE_URL", "http://localhost:3001")

        # 证件照服务配置
        IDPHOTO_SERVICE_URL: str = os.getenv("IDPHOTO_SERVICE_URL", "http://127.0.0.1:8080")

        # 证件照尺寸配置（像素）- 使用英文键名
        IDPHOTO_SIZES = {
            "one_inch": {"width": 295, "height": 413},
            "two_inch": {"width": 413, "height": 579},
            "big_one_inch": {"width": 390, "height": 567},
            "small_one_inch": {"width": 259, "height": 377},
            "big_two_inch": {"width": 413, "height": 626},
            "small_two_inch": {"width": 413, "height": 531}
        }

        # 证件照颜色配置 - 使用英文键名
        IDPHOTO_COLORS = {
            "transparent": {"color": None, "render": 0},
            "white": {"color": "FFFFFF", "render": 0},
            "blue": {"color": "438EDB", "render": 0},
            "red": {"color": "FF0000", "render": 0},
            "blue_gradient": {"color": "438EDB", "render": 1},
            "red_gradient": {"color": "FF0000", "render": 1}
        }

    # 创建设置实例
    settings = Settings()

    # 验证必要的配置
    def validate_config():
        """验证必要的配置项"""
        if not settings.WECHAT_APP_ID:
            print("警告: WECHAT_APP_ID 未设置")
        if not settings.WECHAT_APP_SECRET:
            print("警告: WECHAT_APP_SECRET 未设置")
        if settings.SECRET_KEY == "your-secret-key-change-in-production":
            print("警告: 请在生产环境中更改 SECRET_KEY")

if __name__ == "__main__":
    validate_config()
