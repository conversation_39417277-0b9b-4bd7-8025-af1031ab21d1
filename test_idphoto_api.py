"""
测试证件照API功能
"""
import requests
import json
import base64
import io
import os
from PIL import Image

# 服务器配置
BASE_URL = "http://localhost:18080"

def create_test_image():
    """创建一个测试用的图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (400, 600), color='lightblue')
    
    # 在图片上画一个简单的人脸轮廓（用于测试）
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    
    # 画一个圆形作为头部
    draw.ellipse([150, 100, 250, 200], fill='pink', outline='black')
    # 画眼睛
    draw.ellipse([170, 130, 180, 140], fill='black')
    draw.ellipse([220, 130, 230, 140], fill='black')
    # 画嘴巴
    draw.arc([180, 160, 220, 180], 0, 180, fill='black')
    
    # 保存为字节流
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def get_auth_token():
    """获取认证token（模拟登录）"""
    login_data = {
        "code": "mock_code_for_testing",
        "user_info": {
            "nickName": "证件照测试用户",
            "avatarUrl": "https://example.com/avatar.jpg",
            "gender": 1,
            "country": "中国",
            "province": "北京",
            "city": "北京"
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"登录失败: {response.status_code}, {response.text}")
            return None
    except Exception as e:
        print(f"登录请求失败: {e}")
        return None

def test_idphoto_health():
    """测试证件照服务健康状态"""
    print("\n=== 测试证件照服务健康状态 ===")
    try:
        response = requests.get(f"{BASE_URL}/idphoto/health")
        print(f"健康检查状态: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_get_sizes():
    """测试获取支持的尺寸列表"""
    print("\n=== 测试获取支持的尺寸列表 ===")
    try:
        response = requests.get(f"{BASE_URL}/idphoto/sizes")
        print(f"获取尺寸列表状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"支持的尺寸数量: {len(data['data']['sizes'])}")
            for size in data['data']['sizes']:
                print(f"  - {size['name']}: {size['width']}x{size['height']}")
        return response.status_code == 200
    except Exception as e:
        print(f"获取尺寸列表失败: {e}")
        return False

def test_get_colors():
    """测试获取支持的颜色列表"""
    print("\n=== 测试获取支持的颜色列表 ===")
    try:
        response = requests.get(f"{BASE_URL}/idphoto/colors")
        print(f"获取颜色列表状态: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"支持的颜色数量: {len(data['data']['colors'])}")
            for color in data['data']['colors']:
                print(f"  - {color['name']}: {color['hex']} (渲染模式: {color['render']})")
        return response.status_code == 200
    except Exception as e:
        print(f"获取颜色列表失败: {e}")
        return False

def test_generate_idphoto(token, size="one_inch", color="white"):
    """测试生成证件照"""
    print(f"\n=== 测试生成证件照 (尺寸: {size}, 颜色: {color}) ===")
    
    if not token:
        print("需要认证token")
        return False
    
    try:
        # 创建测试图片
        test_image = create_test_image()
        
        # 准备请求数据
        files = {
            'image': ('test_photo.jpg', test_image, 'image/jpeg')
        }
        data = {
            'size': size,
            'color': color
        }
        headers = {
            'Authorization': f'Bearer {token}'
        }
        
        print(f"发送证件照生成请求...")
        response = requests.post(
            f"{BASE_URL}/idphoto/generate",
            files=files,
            data=data,
            headers=headers,
            timeout=120  # 较长的超时时间
        )
        
        print(f"生成证件照状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"生成成功: {result['message']}")
            
            # 检查返回的数据
            if result.get('data'):
                data = result['data']
                print(f"尺寸: {data.get('size')}")
                print(f"颜色: {data.get('color')}")
                print(f"图片尺寸: {data.get('dimensions')}")
                
                # 如果有base64图片数据，保存为文件
                if data.get('image_base64'):
                    try:
                        img_data = base64.b64decode(data['image_base64'])
                        filename = f"test_idphoto_{size}_{color}.jpg"
                        with open(filename, 'wb') as f:
                            f.write(img_data)
                        print(f"证件照已保存为: {filename}")
                    except Exception as e:
                        print(f"保存图片失败: {e}")
            
            return True
        else:
            print(f"生成失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"生成证件照请求失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始测试证件照API...")
    
    # 测试基础功能
    health_ok = test_idphoto_health()
    sizes_ok = test_get_sizes()
    colors_ok = test_get_colors()
    
    if not all([health_ok, sizes_ok, colors_ok]):
        print("\n基础功能测试失败，停止后续测试")
        return
    
    # 获取认证token
    print("\n=== 获取认证token ===")
    token = get_auth_token()
    if not token:
        print("无法获取认证token，停止证件照生成测试")
        return
    
    print(f"获取到token: {token[:20]}...")
    
    # 测试不同尺寸和颜色的证件照生成
    test_cases = [
        ("one_inch", "white"),
        ("two_inch", "blue"),
        ("big_one_inch", "red"),
        ("small_one_inch", "transparent"),
        ("one_inch", "blue_gradient"),
    ]
    
    success_count = 0
    for size, color in test_cases:
        if test_generate_idphoto(token, size, color):
            success_count += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"总测试用例: {len(test_cases)}")
    print(f"成功: {success_count}")
    print(f"失败: {len(test_cases) - success_count}")
    
    if success_count == len(test_cases):
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查服务配置")

if __name__ == "__main__":
    run_all_tests()
