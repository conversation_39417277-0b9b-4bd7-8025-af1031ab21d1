
"""
环境配置文件 (已弃用)
请使用 config/ 目录下的新配置文件
"""
import warnings

# 发出弃用警告
warnings.warn(
    "env.py 已弃用，请使用 config/ 目录下的新配置文件",
    DeprecationWarning,
    stacklevel=2
)

# 为了向后兼容，从新配置导入
try:
    from config.fastapi_config import settings
    RESUME_SERVER_PORT = settings.PORT
    PDF_SERVICE_URL = settings.PDF_SERVICE_URL
except ImportError:
    # 如果新配置不可用，使用默认值
    RESUME_SERVER_PORT = 18080
    PDF_SERVICE_URL = "http://localhost:3001"