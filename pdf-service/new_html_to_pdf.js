const puppeteer = require('puppeteer-core');
const path = require('path');
const fs = require('fs');

// 加载配置
const config = require('../config/pdf_service_config');
const { Browser, BrowserPool, chromePath } = require('./browserpool.cjs');

// 使用配置创建浏览器池
const browserpool = new BrowserPool({
  initialSize: config.BROWSER_POOL.initialSize,
  maxBrowsers: config.BROWSER_POOL.maxBrowsers,
  maxPagesPerBrowser: config.BROWSER_POOL.maxPagesPerBrowser,
  maxTotalUsage: config.BROWSER_POOL.maxTotalUsage,
  maxAvgUsage: config.BROWSER_POOL.maxAvgUsage,
  intervalHealthCheckCreateNewBrowser: config.BROWSER_POOL.intervalHealthCheckCreateNewBrowser,
  browserOptions: {
    ...config.PUPPETEER,
    executablePath: chromePath,
  }
});

// 初始化浏览器池
console.log('初始化浏览器池...');
browserpool.initializePool();

const MAX_RETRIES = 3; // 最大重试次数
const RETRY_DELAY = 3000; // 重试延迟增加到3秒，给系统更多恢复时间

/**
 * 处理HTML内容，返回准备好的页面
 * @param {string} html - 要处理的HTML内容
 * @param {Object} options - 处理选项
 * @param {string} outputType - 输出类型 ('pdf' 或 'jpeg')，用于设置不同的viewport
 * @returns {Promise<Object>} - 返回处理结果，包含页面对象和性能指标
 */
async function processHTML(html, options = {}, outputType = 'pdf') {
  let page = null;
  let pageId = null;
  let browserId = null;

  // 记录性能指标
  const performance = {
    start: Date.now(),
    getPage: 0,
    setContent: 0,
    resourceLoad: 0,
    total: 0
  };

  try {
    console.log('处理HTML开始执行...');
    console.log('收到HTML内容长度:', html?.length);

    // 1. 获取浏览器页面
    console.log('准备获取浏览器实例...');
    const getPageStart = Date.now();
    const pageInfo = await browserpool.getPage();
    page = pageInfo.page;
    pageId = pageInfo.pageId;
    browserId = pageInfo.browserId;
    performance.getPage = Date.now() - getPageStart;

    // 2. 设置视口 - 根据输出类型设置不同的viewport
    // 使用更高的分辨率以获得更好的图片质量
    let viewportConfig;
    if (outputType === 'jpeg') {
      // JPEG预览图：A4宽度，但高度设置为较大值以适应内容
      viewportConfig = {
        width: 794,  // A4宽度，约210mm
        height: 1123, // 较大的高度，让内容自然展开
        deviceScaleFactor: 2, // 保持高分辨率
      };
      console.log('设置JPEG预览图viewport: A4宽度，动态高度');
    } else {
      // PDF：标准A4比例
      viewportConfig = {
        width: 794,  // A4宽度，约210mm
        height: 1123, // A4高度，约297mm
        deviceScaleFactor: 2, // 保持高分辨率
      };
      console.log('设置PDF viewport: 标准A4比例');
    }

    await page.setViewport(viewportConfig);
    console.log('新页面创建成功');

    // 3. 设置页面内容
    console.log('设置页面内容...');
    const setContentStart = Date.now();
    try {
      // 使用更轻量级的方式设置内容，优化性能
      await Promise.race([
        page.setContent(html, {
          // waitUntil: 'domcontentloaded',   // 只等待DOM加载完成，不等待所有资源
          waitUntil: [
            'domcontentloaded',
            // 'networkidle0'
          ],
          timeout: 30000, // 超时时间
        }),
        // 额外的超时保护
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('设置页面内容超时')), 35000)
        )
      ]);
    } catch (setContentError) {
      console.error(`设置页面内容失败: ${setContentError.message}`);

      // 如果是超时错误，尝试更简单的渲染策略
      if (setContentError.message.includes('timeout')) {
        console.log('尝试备用渲染策略...');

        // 先访问空白页
        await page.goto('about:blank', { waitUntil: 'networkidle0', timeout: 5000 })
          .catch(e => console.warn(`导航到空白页失败: ${e.message}`));

        // 使用更基本的渲染策略
        await page.setContent(html, {
          waitUntil: 'domcontentloaded', // 只等待DOM加载完成
          timeout: 30000,
        });

        // 手动等待页面渲染
        await page.evaluate(() => {
          return new Promise(resolve => {
            // 给页面一些时间渲染
            setTimeout(resolve, 500);
          });
        });
      }
    }
    performance.setContent = Date.now() - setContentStart;
    console.log(`setContent 耗时: ${performance.setContent}ms`);

    // 4. 等待资源加载
    console.log('等待关键资源加载完成...');
    const resourceLoadStart = Date.now();
    await Promise.race([
      page.evaluate(() => {
        return new Promise((resolve) => {
          // 只检查可见区域内的图片
          const checkVisibleImages = () => {
            // 获取可见区域内的图片
            const images = Array.from(document.querySelectorAll('img')).filter(img => {
              const rect = img.getBoundingClientRect();
              return (
                rect.top < window.innerHeight &&
                rect.left < window.innerWidth &&
                rect.bottom > 0 &&
                rect.right > 0
              );
            });

            if (images.length === 0) {
              console.log('可见区域内没有图片');
              return true;
            }

            const totalImages = images.length;
            const loadedImages = images.filter(img => img.complete).length;

            if (loadedImages !== totalImages) {
              console.log(`可见图片加载进度: ${loadedImages}/${totalImages}`);
            }

            return loadedImages === totalImages;
          };

          // 快速检查函数
          const check = () => {
            if (checkVisibleImages()) {
              console.log('关键图片已加载完成');
              resolve();
              return;
            }

            // 最多等待3秒
            const startTime = window.performance && window.performance.now ? window.performance.now() : Date.now();
            if (Date.now() - startTime > 3000) {
              console.log('图片加载等待时间已达上限，继续执行');
              resolve();
              return;
            }

            // 继续等待，但缩短检查间隔
            setTimeout(check, 200);
          };

          // 开始检查
          check();
        });
      }),
      // 缩短全局超时保护
      new Promise(resolve => setTimeout(() => {
        console.log('资源加载全局超时保护触发');
        resolve();
      }, 4000))
    ]);
    performance.resourceLoad = Date.now() - resourceLoadStart;
    console.log(`资源加载等待耗时: ${performance.resourceLoad}ms`);

    console.log('页面内容设置成功');

    // 计算总处理时间
    performance.total = Date.now() - performance.start;

    // 返回处理结果
    return {
      page,
      pageId,
      browserId,
      performance
    };

  } catch (error) {
    console.error('处理HTML过程中发生错误:', error);
    console.error('错误堆栈:', error.stack);

    // 清理资源
    if (page) {
      try {
        await browserpool.releasePage({ pageId, browserId });
      } catch (e) {
        console.error('释放页面失败:', e);
      }
    }

    throw error;
  }
}

/**
 * 将HTML转换为PDF
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Object} options.pdfOptions - 传递给page.pdf()的选项
 * @param {string} options.savePath - 保存PDF的路径（可选）
 * @returns {Promise<Buffer>} - 返回PDF数据的Buffer
 */
async function generatePDF(html, options = {}) {
  const {
    pdfOptions = {
      format: 'A4',
      printBackground: true,
      preferCSSPageSize: true,
      margin: {
        top: '0mm',
        right: '0mm',
        bottom: '0mm',
        left: '0mm'
      },
      displayHeaderFooter: false,
      // 强制分页，确保按A4页面大小分页
      scale: 1.0 // 确保1:1缩放
    },
    savePath = null
  } = options;

  let page = null;
  let pageId = null;
  let browserId = null;

  try {
    console.log('generatePDF 开始执行...');

    // 使用通用的HTML处理函数，指定为PDF输出类型
    const result = await processHTML(html, options, 'pdf');
    page = result.page;
    pageId = result.pageId;
    browserId = result.browserId;

    // 生成PDF
    console.log('开始生成PDF...');
    const pdfStart = Date.now();

    const pdf = await page.pdf({
      ...pdfOptions
    });

    const pdfTime = Date.now() - pdfStart;
    console.log(`PDF生成成功，耗时: ${pdfTime}ms`);

    // 关闭页面
    await browserpool.releasePage({ pageId, browserId });
    console.log('浏览器实例已释放回池中');

    // 只在明确需要时保存PDF到本地
    if (savePath) {
      fs.writeFileSync(savePath, pdf);
      console.log(`PDF已保存到: ${savePath}`);
    }

    // 将Uint8Array转换为Buffer
    const buffer = Buffer.from(pdf);

    // 输出总体性能报告
    console.log('----------------------------------------');
    console.log('PDF生成性能报告:');
    console.log(`总耗时: ${result.performance.total + pdfTime}ms`);
    console.log(`获取页面: ${result.performance.getPage}ms`);
    console.log(`设置内容: ${result.performance.setContent}ms`);
    console.log(`资源加载: ${result.performance.resourceLoad}ms`);
    console.log(`PDF生成: ${pdfTime}ms`);

    return buffer;

  } catch (error) {
    console.error('生成PDF过程中发生错误:', error);

    // 清理资源
    if (page && pageId && browserId) {
      try {
        await browserpool.releasePage({ pageId, browserId });
      } catch (e) {
        console.error('释放页面失败:', e);
      }
    }

    throw error;
  }
}

/**
 * 将HTML转换为JPEG图片
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Object} options.imageOptions - 传递给page.screenshot()的选项
 * @param {string} options.savePath - 保存图片的路径（可选）
 * @returns {Promise<Buffer>} - 返回图片数据的Buffer
 */
async function generateJPEG(html, options = {}) {
  const {
    imageOptions = {
      type: 'jpeg',
      quality: 90,
      fullPage: true
    },
    savePath = null
  } = options;

  let page = null;
  let pageId = null;
  let browserId = null;

  try {
    console.log('generateJPEG 开始执行...');

    // 使用通用的HTML处理函数，指定为JPEG输出类型
    const result = await processHTML(html, options, 'jpeg');
    page = result.page;
    pageId = result.pageId;
    browserId = result.browserId;

    // 生成JPEG图片
    console.log('开始生成JPEG图片...');
    const screenshotStart = Date.now();

    // 使用全页面截图模式
    console.log('使用全页面截图模式生成预览图...');

    const optimizedOptions = {
      ...imageOptions,
      omitBackground: false, // 保留背景以确保一致性
      optimizeForSpeed: true, // 优化速度
      captureBeyondViewport: false, // 不捕获视口之外的内容
      fullPage: true // 截取完整页面
    };

    // 添加超时保护
    const jpeg = await Promise.race([
      page.screenshot(optimizedOptions),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('截图操作超时')), 10000)
      )
    ]);

    console.log('全页面截图生成完成');

    const screenshotTime = Date.now() - screenshotStart;
    console.log(`JPEG图片生成成功，耗时: ${screenshotTime}ms`);

    // 关闭页面
    await browserpool.releasePage({ pageId, browserId });
    console.log('浏览器实例已释放回池中');

    // if savePath is null, set default path
    // if (!savePath) {
    //   const fallback_savePath = path.join(__dirname, '../output', `resume_${pageId}.jpeg`);
    //   fs.writeFileSync(fallback_savePath, jpeg);
    //   console.log(`图片已保存到: ${fallback_savePath}`);
    // }
    // else
    // {
    //   fs.writeFileSync(savePath, jpeg);
    //   console.log(`图片已保存到: ${savePath}`);
    // }

    // 将Uint8Array转换为Buffer
    const buffer = Buffer.from(jpeg);

    // 输出总体性能报告
    console.log('----------------------------------------');
    console.log('JPEG生成性能报告:');
    console.log(`总耗时: ${result.performance.total + screenshotTime}ms`);
    console.log(`获取页面: ${result.performance.getPage}ms`);
    console.log(`设置内容: ${result.performance.setContent}ms`);
    console.log(`资源加载: ${result.performance.resourceLoad}ms`);
    console.log(`截图生成: ${screenshotTime}ms`);

    return buffer;

  } catch (error) {
    console.error('生成JPEG图片过程中发生错误:', error);

    // 清理资源
    if (page && pageId && browserId) {
      try {
        await browserpool.releasePage({ pageId, browserId });
      } catch (e) {
        console.error('释放页面失败:', e);
      }
    }

    throw error;
  }
}

/**
 * 通用的带重试的HTML转换函数
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @param {Function} generatorFn - 生成函数 (generatePDF 或 generateJPEG)
 * @param {string} outputType - 输出类型描述 ('PDF' 或 'JPEG')
 * @returns {Promise<Buffer>} - 返回生成数据的Buffer
 */
async function generateWithRetry(html, options = {}, generatorFn, outputType) {
  let retryCount = 0;
  let lastError = null;

  // 使用指数退避策略，每次重试延迟时间增加
  const getRetryDelay = (attempt) => {
    return RETRY_DELAY * Math.pow(2, attempt); // 指数增长: 3秒, 6秒, 12秒...
  };

  while (retryCount < MAX_RETRIES) {
    try {
      console.log(`第 ${retryCount + 1} 次尝试生成${outputType}...`);

      // 在重试前尝试清理内存
      if (retryCount > 0) {
        console.log('执行内存清理...');
        global.gc && global.gc(); // 如果启用了--expose-gc，则执行垃圾回收
      }

      const buffer = await generatorFn(html, options);

      // 验证生成的数据是否有效
      if (!buffer || buffer.length < 100) {
        throw new Error(`生成的${outputType}数据无效或过小`);
      }

      console.log(`成功生成${outputType}，大小: ${buffer.length} 字节`);
      return buffer;
    } catch (error) {
      lastError = error;
      retryCount++;

      // 记录详细错误信息
      console.error(`第 ${retryCount} 次尝试失败:`, error.message);
      if (error.stack) {
        console.error(`错误堆栈: ${error.stack}`);
      }

      if (retryCount < MAX_RETRIES) {
        const delay = getRetryDelay(retryCount - 1);
        console.log(`等待 ${delay}ms 后进行第 ${retryCount + 1} 次重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        console.error(`已达到最大重试次数 ${MAX_RETRIES}，转换失败`);
        throw new Error(`${outputType}生成失败，已尝试 ${MAX_RETRIES} 次: ${lastError.message}`);
      }
    }
  }
}

/**
 * 带重试的HTML到PDF转换
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @returns {Promise<Buffer>} - 返回PDF数据的Buffer
 */
async function generatePDFWithRetry(html, options = {}) {
  return generateWithRetry(html, options, generatePDF, 'PDF');
}

/**
 * 带重试的HTML到JPEG转换
 * @param {string} html - 要转换的HTML内容
 * @param {Object} options - 转换选项
 * @returns {Promise<Buffer>} - 返回JPEG数据的Buffer
 */
async function generateJPEGWithRetry(html, options = {}) {
  return generateWithRetry(html, options, generateJPEG, 'JPEG');
}

// // 优雅关闭
// async function gracefulShutdown(signal) {
//   console.log(`\n收到 ${signal} 信号，正在关闭浏览器池...`);

//   const timeout = setTimeout(() => {
//     console.error('关闭超时，强制退出');
//     process.exit(1);
//   }, 10000);

//   try {
//     browserpool.closeAllBrowsers();
//     clearTimeout(timeout);
//     console.log('浏览器池已成功关闭');
//     process.exit(0);
//   } catch (error) {
//     clearTimeout(timeout);
//     console.error('关闭浏览器池失败:', error);
//     process.exit(1);
//   }
// }

// // 注册所有信号
// ['SIGINT', 'SIGTERM', 'SIGHUP', 'SIGUSR2'].forEach(signal => {
//   process.on(signal, () => gracefulShutdown(signal));
// });

// 模块导出
module.exports = {
  generatePDF,
  generatePDFWithRetry,
  generateJPEG,
  generateJPEGWithRetry
};