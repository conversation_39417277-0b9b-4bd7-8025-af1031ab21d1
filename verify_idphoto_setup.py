#!/usr/bin/env python3
"""
验证证件照API设置是否正确
"""
import sys
import importlib.util

def check_imports():
    """检查所有必要的模块是否可以正常导入"""
    print("=== 检查模块导入 ===")
    
    modules_to_check = [
        "app.routers.idphoto",
        "app.services.idphoto_service", 
        "app.schemas.idphoto",
        "app.config"
    ]
    
    all_good = True
    
    for module_name in modules_to_check:
        try:
            module = importlib.import_module(module_name)
            print(f"✅ {module_name} - 导入成功")
        except ImportError as e:
            print(f"❌ {module_name} - 导入失败: {e}")
            all_good = False
        except Exception as e:
            print(f"⚠️  {module_name} - 导入异常: {e}")
            all_good = False
    
    return all_good

def check_config():
    """检查配置是否正确"""
    print("\n=== 检查配置 ===")
    
    try:
        from app.config import settings
        
        # 检查证件照服务URL
        print(f"证件照服务URL: {settings.IDPHOTO_SERVICE_URL}")
        
        # 检查尺寸配置
        sizes = settings.IDPHOTO_SIZES
        print(f"支持的尺寸数量: {len(sizes)}")
        for size_name, size_config in sizes.items():
            print(f"  - {size_name}: {size_config['width']}x{size_config['height']}")
        
        # 检查颜色配置
        colors = settings.IDPHOTO_COLORS
        print(f"支持的颜色数量: {len(colors)}")
        for color_name, color_config in colors.items():
            print(f"  - {color_name}: {color_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def check_router_registration():
    """检查路由是否正确注册"""
    print("\n=== 检查路由注册 ===")
    
    try:
        from main import app
        
        # 获取所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        # 检查证件照相关路由
        idphoto_routes = [route for route in routes if route.startswith('/idphoto')]
        
        if idphoto_routes:
            print(f"✅ 找到 {len(idphoto_routes)} 个证件照路由:")
            for route in idphoto_routes:
                print(f"  - {route}")
            return True
        else:
            print("❌ 未找到证件照路由")
            return False
            
    except Exception as e:
        print(f"❌ 路由检查失败: {e}")
        return False

def check_schemas():
    """检查数据模型是否正确定义"""
    print("\n=== 检查数据模型 ===")
    
    try:
        from app.schemas.idphoto import IDPhotoSize, IDPhotoColor, IDPhotoRequest, IDPhotoResponse
        
        # 检查枚举值
        sizes = [size.value for size in IDPhotoSize]
        colors = [color.value for color in IDPhotoColor]
        
        print(f"✅ IDPhotoSize 枚举: {sizes}")
        print(f"✅ IDPhotoColor 枚举: {colors}")
        
        # 测试创建请求对象
        request = IDPhotoRequest(size=IDPhotoSize.ONE_INCH, color=IDPhotoColor.WHITE)
        print(f"✅ 请求模型测试: size={request.size}, color={request.color}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型检查失败: {e}")
        return False

def main():
    """主函数"""
    print("证件照API设置验证工具")
    print("=" * 50)
    
    checks = [
        ("模块导入", check_imports),
        ("配置检查", check_config),
        ("路由注册", check_router_registration),
        ("数据模型", check_schemas)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查过程中出现异常: {e}")
            results.append((check_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("验证结果总结:")
    
    all_passed = True
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有检查都通过了！证件照API已正确设置。")
        print("\n下一步:")
        print("1. 启动主服务: python main.py")
        print("2. 确保证件照后端服务运行在 http://127.0.0.1:8080")
        print("3. 运行测试: python test_idphoto_api.py")
    else:
        print("\n⚠️  部分检查失败，请修复上述问题后重新验证。")
        sys.exit(1)

if __name__ == "__main__":
    main()
